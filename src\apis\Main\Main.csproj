﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>d213a56a-4761-462c-8d92-b4b205b2b240</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="FluentValidation" />

	  <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="NLog" />
	  <PackageReference Include="Asp.Versioning.Mvc" />
	  <PackageReference Include="Marvin.Cache.Headers" />
	  <PackageReference Include="Swashbuckle.AspNetCore" />
	  <PackageReference Include="AspNetCoreRateLimit" />
	  <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\Abstraction\Abstraction.csproj" />
    <ProjectReference Include="..\..\Core\Application\Application.csproj" />
    <ProjectReference Include="..\..\Infrastructure\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\..\Infrastructure\Presentation\Presentation.csproj" />
  </ItemGroup>
	
</Project>