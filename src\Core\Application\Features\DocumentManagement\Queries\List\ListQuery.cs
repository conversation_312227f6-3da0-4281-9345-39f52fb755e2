using Abstraction.Base.Dto;
using Abstraction.Common.Wappers;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Domain.Entities.DocumentManagement;

namespace Application.Features.DocumentManagement.Queries.List
{
    /// <summary>
    /// Query for retrieving documents with filtering and pagination
    /// Implements CQRS pattern using MediatR
    /// Follows BoardMembers ListQuery pattern for consistency
    /// </summary>
    public record ListQuery : BaseListDto, IQuery<PaginatedResult<DocumentDto>>
    {
        /// <summary>
        /// Document category ID filter
        /// </summary>
        public int CategoryId { get; set; }
        /// <summary>
        /// Fund ID filter
        /// </summary>
        public int FundId { get; set; }

    }
}
