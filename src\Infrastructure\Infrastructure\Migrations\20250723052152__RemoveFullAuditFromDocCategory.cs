﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class _RemoveFullAuditFromDocCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_CreatedBy",
                table: "DocumentCategories");

            migrationBuilder.DropForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_DeletedBy",
                table: "DocumentCategories");

            migrationBuilder.DropForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_UpdatedBy",
                table: "DocumentCategories");

            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_CreatedBy",
                table: "DocumentCategories");

            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_DeletedBy",
                table: "DocumentCategories");

            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_UpdatedBy",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "DocumentCategories");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "DocumentCategories");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "Documents",
                type: "datetime2",
                nullable: true,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeleted",
                table: "Documents",
                type: "bit",
                nullable: true,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "Documents",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<string>(
                name: "NameEn",
                table: "DocumentCategories",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "NameAr",
                table: "DocumentCategories",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_DisplayOrder",
                table: "DocumentCategories",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_NameAr",
                table: "DocumentCategories",
                column: "NameAr");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_NameEn",
                table: "DocumentCategories",
                column: "NameEn");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_DisplayOrder",
                table: "DocumentCategories");

            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_NameAr",
                table: "DocumentCategories");

            migrationBuilder.DropIndex(
                name: "IX_DocumentCategories_NameEn",
                table: "DocumentCategories");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "Documents",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeleted",
                table: "Documents",
                type: "bit",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true,
                oldDefaultValue: false);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "Documents",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<string>(
                name: "NameEn",
                table: "DocumentCategories",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "NameAr",
                table: "DocumentCategories",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "DocumentCategories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "DocumentCategories",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedAt",
                table: "DocumentCategories",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "DocumentCategories",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "DocumentCategories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "DocumentCategories",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "DocumentCategories",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_CreatedBy",
                table: "DocumentCategories",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_DeletedBy",
                table: "DocumentCategories",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentCategories_UpdatedBy",
                table: "DocumentCategories",
                column: "UpdatedBy");

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_CreatedBy",
                table: "DocumentCategories",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_DeletedBy",
                table: "DocumentCategories",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentCategories_AspNetUsers_UpdatedBy",
                table: "DocumentCategories",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
