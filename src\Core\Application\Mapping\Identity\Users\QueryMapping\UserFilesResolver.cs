using AutoMapper;
using Abstraction.Contracts.Identity;
using Domain.Entities.Users;
using Abstraction.Contracts.Logger;
using Application.Features.Identity.Users.Queries.Responses;
using Application.Common.Helpers;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Application.Features.Identity.Users.Dtos;
using Abstraction.Contract.Service.Storage;
using Abstraction.Enums;
using System.Threading;
using Domain.Entities.Shared;
using DocumentFormat.OpenXml.Bibliography;
using Application.Features.Resolutions.Dtos;

namespace Application.Mapping.Users
{
    /// <summary>
    /// Custom AutoMapper value resolver for user primary role display
    /// Provides the primary role name for a user with proper filtering and error handling
    /// Note: This resolver is currently not used in favor of the EnhanceWithRoleInformation approach
    /// which provides better performance and error handling
    /// </summary>
    public class UserFilesResolver(IRepositoryManager repository, IServiceManager serviceManager) : IValueResolver<Attachment, AttachmentDto, string>
    {
        private readonly IRepositoryManager _repository = repository;
        private readonly IServiceManager _service = serviceManager;

        public string Resolve(Attachment source, AttachmentDto destination, string destMember, ResolutionContext context)
        {


            try
            {
                // Note: Using async methods in AutoMapper resolvers is not recommended
                // This is why the EnhanceWithRoleInformation approach is preferred
                var attachment =  _repository.Attachments.GetByIdAsync<Attachment>(source.Id, trackChanges: false).GetAwaiter().GetResult();
                if (attachment != null)
                {
                   
                    if (attachment?.Path != null)
                    {
                        // Populate attachment preview URL
                        return _service.PreviewUrlHelper.GeneratePreviewUrlAsync(attachment.Path, attachment.ModuleId, CancellationToken.None).GetAwaiter().GetResult() ?? string.Empty;
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }


            //try
            //{
            //    //if (!string.IsNullOrEmpty(source.CVFilePath))
            //    //{
            //       var attachment =  _repository.Attachments.GetByIdAsync<Attachment>(source.Id, trackChanges: false).GetAwaiter().GetResult();
            //        if (attachment != null)
            //        {
            //            return  "";
            //        }
            //    //}

            //    return string.Empty;
            //}
            //catch (Exception ex)
            //{
            //    return string.Empty;
            //}
        }
    }

}
