using Abstraction.Contracts.Repository;
using Domain.Entities.DocumentManagement;

namespace Abstraction.Contract.Repository.DocumentManagement
{
    /// <summary>
    /// Repository interface for DocumentCategory entity operations
    /// </summary>
    public interface IDocumentCategoryRepository : IGenericRepository
    {
        /// <summary>
        /// Get categories with optional filters
        /// </summary>
        Task<List<DocumentCategory>> GetCategoriesAsync();
    }
}
