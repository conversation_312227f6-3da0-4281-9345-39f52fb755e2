using Application.Features.DocumentManagement.Commands.Add;
using FluentValidation;

namespace Application.Features.DocumentManagement.Validation
{
    /// <summary>
    /// Validator for AddDocumentCommand
    /// </summary>
    public class AddDocumentCommandValidator : AbstractValidator<AddDocumentCommand>
    {
        public AddDocumentCommandValidator()
        {
        
            RuleFor(x => x.DocumentCategoryId)
                .GreaterThan(0).WithMessage("Valid document category is required");

            RuleFor(x => x.AttachmentId)
                .NotNull().WithMessage("File is required")
               .GreaterThan(0).WithMessage("Valid File is required");

            RuleFor(x => x.FundId)
                .NotNull().WithMessage("Fund is required")
               .GreaterThan(0).WithMessage("Valid FundId is required");
        }
    }
}
