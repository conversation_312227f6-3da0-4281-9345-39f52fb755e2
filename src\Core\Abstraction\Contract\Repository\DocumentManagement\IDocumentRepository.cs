using Abstraction.Contracts.Repository;
using Domain.Entities.DocumentManagement;

namespace Abstraction.Contract.Repository.DocumentManagement
{
    /// <summary>
    /// Repository interface for Document entity operations
    /// </summary>
    public interface IDocumentRepository : IGenericRepository
    {
        /// <summary>
        /// Get documents with filters and includes
        /// </summary>
        IQueryable<Document> GetDocumentsWithFiltersAsync(
            int categoryId,
            int fundId,
            string? searchTerm = null
            );

        /// <summary>
        /// Soft delete document (mark as inactive)
        /// </summary>
        Task<bool> DeleteDocumentAsync(int documentId);
    }
}
