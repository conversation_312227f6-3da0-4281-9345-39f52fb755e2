using Application.Features.DocumentManagement.Commands.DeleteDocument;
using Application.Features.DocumentManagement.Commands.Add;
using Application.Features.DocumentManagement.Queries.List;
using Application.Features.DocumentManagement.Queries.GetDocumentCategories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using Abstraction.Common.Wappers;
using Application.Features.Funds.Dtos;
using Microsoft.AspNetCore.Http;
using Application.Features.DocumentManagement.Dtos;
using Abstraction.Base.Response;

namespace Presentation.Controllers.DocumentManagement
{
    /// <summary>
    /// Controller for document management operations
    /// </summary>
    [Route("api/[controller]")] 
    [ApiController]
    [Authorize]
    public class DocumentController : AppControllerBase
    {
        /// <summary>
        /// Get documents with filtering and pagination
        /// </summary>
        /// <param name="query">Query parameters for filtering documents</param>
        /// <returns>Paginated list of documents</returns>
        [HttpGet("getAll")]
        [ProducesResponseType(typeof(PaginatedResult<DocumentDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDocuments([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get document categories
        /// </summary>
        /// <param name="query">Query parameters for categories</param>
        /// <returns>List of document categories</returns>
        [HttpGet("categories")]
        [ProducesResponseType(typeof(BaseResponse<List<DocumentCategoryDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDocumentCategories([FromQuery] GetDocumentCategoriesQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Add a new document
        /// </summary>
        /// <param name="command">Add document command with file and metadata</param>
        /// <returns>Add result with document information</returns>
        [HttpPost("add")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddDocument([FromBody] AddDocumentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        ///// <summary>
        ///// Preview a document (doesn't increment download count)
        ///// </summary>
        ///// <param name="documentId">Document ID to preview</param>
        ///// <returns>File content for preview</returns>
        //[HttpGet("{documentId}/preview")]
        //public async Task<IActionResult> PreviewDocument(int documentId)
        //{
        //    var query = new DownloadDocumentQuery { DocumentId = documentId, IsPreview = true };
        //    var response = await Mediator.Send(query);

        //    return File(response.Data.FileContent, response.Data.ContentType);
        //}

        /// <summary>
        /// Delete a document
        /// </summary>
        /// <param name="documentId">Document ID to delete</param>
        /// <param name="permanentDelete">Whether to permanently delete the file from disk</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("delete")]
        public async Task<IActionResult> DeleteDocument(int documentId)
        {
            var command = new DeleteDocumentCommand 
            { 
                DocumentId = documentId 
            };
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
    }
}
