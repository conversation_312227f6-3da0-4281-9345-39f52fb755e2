using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using System.Text.RegularExpressions;
using Abstraction.Constants;
using Application.Features.Identity.Users.Commands.AddUser;
using Abstraction.Contracts.Repository;
using Domain.Entities.Shared;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for AddUserCommand with Sprint 3 enhancements
    /// Implements mobile number validation and unique role checking
    /// </summary>
    public class AddUserValidator : AbstractValidator<AddUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;
        public AddUserValidator(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;
            Include(new BaseUserValidator(_localizer));
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {

            // Username Validation (should match mobile number)
            RuleFor(x => x.UserName)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField]);
               // .Must(BeValidSaudiMobileNumber)
              //  .WithMessage(_localizer[SharedResourcesKey.InvalidSaudiMobilePattern]);

            // Role Validation
            RuleFor(x => x.Roles)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired])
                .Must(roles => roles.Count > 0)
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired]);

            // Role Selection Logic Validation
            RuleFor(x => x.Roles)
                .Must(BeValidRoleSelection)
                .WithMessage(_localizer[SharedResourcesKey.EditUserInvalidRoleSelection]);

            // CV file validation
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    return attachment.FileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".doc", StringComparison.OrdinalIgnoreCase) ||
                            attachment.FileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase);
                })
                .WithMessage(_localizer[SharedResourcesKey.InvalidFileType])
                .When(x => x.CvFileId > 0);

            // Business rule: Attachment file size must not exceed 10 MB (Sprint.md requirement)
            RuleFor(x => x.CvFileId)
                .MustAsync(async (cvFileId, cancellation) =>
                {
                    if (cvFileId <= 0) return true; // Optional attachment

                    var attachment = await _repository.Attachments.GetByIdAsync<Attachment>(cvFileId.Value, trackChanges: false);
                    if (attachment == null) return true; // Let attachment existence validation handle this

                    // Check file size - 10 MB = 10 * 1024 * 1024 bytes
                    const long maxFileSizeBytes = 10 * 1024 * 1024;
                    return attachment.FileSize <= maxFileSizeBytes;
                })
                .WithMessage(_localizer[SharedResourcesKey.FileSizeExceedsLimit])
                .When(x => x.CvFileId > 0);

            RuleFor(x => x.PersonalPhoto)
                .Must(BeValidPhotoFile)
                .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidPhotoFile])
                .When(x => !string.IsNullOrWhiteSpace(x.PersonalPhoto));
        }

        /// <summary>
        /// Validates role selection logic according to JDWA-1251 requirements
        /// Multi-select enabled ONLY IF roles are ('Fund Manager' AND 'Board Member') OR ('Associate Fund Manager' AND 'Board Member')
        /// Otherwise, single role selection only
        /// </summary>
        private bool BeValidRoleSelection(List<string> roles)
        {
            if (roles == null || roles.Count == 0)
                return false;

            // Single role is always valid
            if (roles.Count == 1)
                return true;

            // Multi-select is only allowed for specific combinations
            if (roles.Count == 2)
            {
                var hasValidCombination =
                    (roles.Contains(RoleHelper.FundManager) && roles.Contains(RoleHelper.BoardMember)) ||
                    (roles.Contains(RoleHelper.AssociateFundManager) && roles.Contains(RoleHelper.BoardMember));

                return hasValidCombination;
            }

            // More than 2 roles is not allowed
            return false;
        }

        /// <summary>
        /// Validates CV file format and size (string path validation)
        /// </summary>
        private bool BeValidCVFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return true; // Optional field

            // Basic validation - in real implementation, this would validate file path/URL
            // For now, just check if it's a reasonable string
            return filePath.Length <= 500; // Reasonable path length
        }

        /// <summary>
        /// Validates personal photo file format and size (string path validation)
        /// </summary>
        private bool BeValidPhotoFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return true; // Optional field

            // Basic validation - in real implementation, this would validate file path/URL
            // For now, just check if it's a reasonable string
            return filePath.Length <= 500; // Reasonable path length
        }

        /// <summary>
        /// Validates Saudi mobile number format using comprehensive pattern
        /// Supports multiple formats: 009665, 9665, +9665, 05, 5 with proper telecom prefixes
        /// Based on: https://gist.github.com/homaily/8672499
        ///
        /// Valid formats:
        /// - 05XXXXXXXX (10 digits): Local format
        /// - 5XXXXXXXX (9 digits): Short local format
        /// - +9665XXXXXXXX (13 digits): International format
        /// - 9665XXXXXXXX (12 digits): Country code format
        /// - 009665XXXXXXXX (14 digits): Full international format
        /// </summary>
        private bool BeValidSaudiMobileNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Saudi mobile pattern from the gist - exactly as specified
            // This pattern validates the structure: country_code + telecom_prefix + 7_digits
            var saudiMobilePattern = @"^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$";
            return Regex.IsMatch(phoneNumber, saudiMobilePattern);
        }

    }
}
