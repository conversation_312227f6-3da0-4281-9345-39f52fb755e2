using Abstraction.Contract.Repository.DocumentManagement;
using Abstraction.Contract.Service;
using Domain.Entities.DocumentManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.DocumentManagement
{
    /// <summary>
    /// Repository implementation for DocumentCategory entity operations
    /// </summary>
    public class DocumentCategoryRepository : GenericRepository, IDocumentCategoryRepository
    {
        #region Constructor
        public DocumentCategoryRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get categories with optional filters
        /// </summary>
        public async Task<List<DocumentCategory>> GetCategoriesAsync()
        { 
            return await RepositoryContext.Set<DocumentCategory>().AsQueryable()
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();
        }
        #endregion
    }
}
