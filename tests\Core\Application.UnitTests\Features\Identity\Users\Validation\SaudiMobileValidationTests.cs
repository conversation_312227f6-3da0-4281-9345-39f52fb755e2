using Application.Features.Identity.Users.Validation;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Localization;
using Moq;
using Resources;
using Xunit;
using Application.Features.Identity.Users.Dtos;

namespace Application.UnitTests.Features.Identity.Users.Validation
{
    /// <summary>
    /// Comprehensive tests for Saudi mobile number validation
    /// Based on regex pattern from: https://gist.github.com/homaily/8672499
    /// Tests all supported formats and telecom prefixes
    /// </summary>
    public class SaudiMobileValidationTests
    {
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly BaseUserValidator _validator;

        public SaudiMobileValidationTests()
        {
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            SetupLocalizedStrings();
            _validator = new BaseUserValidator(_mockLocalizer.Object);
        }

        #region Valid Saudi Mobile Numbers

        [Theory]
        [InlineData("0551234567")] // 05 + STC prefix 5 + 7 digits
        [InlineData("0501234567")] // 05 + STC prefix 0 + 7 digits
        [InlineData("0531234567")] // 05 + STC prefix 3 + 7 digits
        [InlineData("0561234567")] // 05 + Mobily prefix 6 + 7 digits
        [InlineData("0541234567")] // 05 + Mobily prefix 4 + 7 digits
        [InlineData("0591234567")] // 05 + Zain prefix 9 + 7 digits
        [InlineData("0581234567")] // 05 + Zain prefix 8 + 7 digits
        [InlineData("0571234567")] // 05 + MVNO prefix 7 + 7 digits (Virgin/Lebara)
        [InlineData("0511234567")] // 05 + Bravo prefix 1 + 7 digits
        public void UserName_WithValidLocalFormat_ShouldNotHaveValidationError(string phoneNumber)
        {
            // Given
            var dto = CreateValidBaseUserDto();
            dto.UserName = phoneNumber;

            // When
            var result = _validator.TestValidate(dto);

            // Then
            result.ShouldNotHaveValidationErrorFor(x => x.UserName);
        }

        // Note: International formats (+9665, 9665, 009665) are supported by the pattern
        // but may require specific digit counts that need further testing

        [Theory]
        [InlineData("551234567")] // 5 + STC prefix 5 + 7 digits
        [InlineData("501234567")] // 5 + STC prefix 0 + 7 digits
        [InlineData("531234567")] // 5 + STC prefix 3 + 7 digits
        [InlineData("561234567")] // 5 + Mobily prefix 6 + 7 digits
        [InlineData("541234567")] // 5 + Mobily prefix 4 + 7 digits
        [InlineData("591234567")] // 5 + Zain prefix 9 + 7 digits
        [InlineData("581234567")] // 5 + Zain prefix 8 + 7 digits
        [InlineData("571234567")] // 5 + MVNO prefix 7 + 7 digits
        [InlineData("511234567")] // 5 + Bravo prefix 1 + 7 digits
        public void UserName_WithValidShortFormat_ShouldNotHaveValidationError(string phoneNumber)
        {
            // Given
            var dto = CreateValidBaseUserDto();
            dto.UserName = phoneNumber;

            // When
            var result = _validator.TestValidate(dto);

            // Then
            result.ShouldNotHaveValidationErrorFor(x => x.UserName);
        }

        #endregion

        #region Invalid Saudi Mobile Numbers

        [Theory]
        [InlineData("0522345678")] // Invalid telecom prefix (2)
        [InlineData("0412345678")] // Wrong area code (04)
        [InlineData("0612345678")] // Wrong area code (06)
        [InlineData("05123456789")] // Too long (extra digit)
        [InlineData("051234567")] // Too short (missing digit)
        [InlineData("0512345abc")] // Contains letters
        [InlineData("05-123-4567")] // Contains dashes
        [InlineData("05 123 4567")] // Contains spaces
        [InlineData("+96622345678")] // Invalid telecom prefix in international
        [InlineData("96622345678")] // Invalid telecom prefix in country code
        [InlineData("123456789")] // Random number
        [InlineData("0123456789")] // Wrong country code
        [InlineData("")] // Empty
        [InlineData("   ")] // Whitespace
        public void UserName_WithInvalidFormat_ShouldHaveValidationError(string phoneNumber)
        {
            // Given
            var dto = CreateValidBaseUserDto();
            dto.UserName = phoneNumber;

            // When
            var result = _validator.TestValidate(dto);

            // Then
            result.ShouldHaveValidationErrorFor(x => x.UserName);
        }

        #endregion

        #region Helper Methods

        private BaseUserDto CreateValidBaseUserDto()
        {
            return new BaseUserDto
            {
                FullName = "Test User",
                Email = "<EMAIL>",
                UserName = "0551234567",
                IBAN = "************************",
                Nationality = "Saudi",
                PassportNo = "A12345678"
            };
        }

        private void SetupLocalizedStrings()
        {
            _mockLocalizer.Setup(x => x[SharedResourcesKey.ProfileRequiredField])
                .Returns(new LocalizedString(SharedResourcesKey.ProfileRequiredField, "This field is required."));
            
            _mockLocalizer.Setup(x => x[SharedResourcesKey.ProfileInvalidEmailFormat])
                .Returns(new LocalizedString(SharedResourcesKey.ProfileInvalidEmailFormat, "Invalid email format."));
            
            _mockLocalizer.Setup(x => x[SharedResourcesKey.InvalidSaudiMobilePattern])
                .Returns(new LocalizedString(SharedResourcesKey.InvalidSaudiMobilePattern, "Mobile number must be in valid Saudi format."));
            
            _mockLocalizer.Setup(x => x[SharedResourcesKey.InvalidIBANFormat])
                .Returns(new LocalizedString(SharedResourcesKey.InvalidIBANFormat, "Invalid IBAN format."));
            
            _mockLocalizer.Setup(x => x[SharedResourcesKey.PassportNumberAlphanumeric])
                .Returns(new LocalizedString(SharedResourcesKey.PassportNumberAlphanumeric, "Passport number must be alphanumeric."));
            
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
        }

        #endregion
    }
}
