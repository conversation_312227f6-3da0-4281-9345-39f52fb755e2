using Abstraction.Base.Dto;

namespace Application.Features.DocumentManagement.Dtos
{
    /// <summary>
    /// Document data transfer object
    /// </summary>
    public record DocumentDto : BaseDto
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Formatted file size (e.g., "1.5 MB")
        /// </summary>
        public string FormattedFileSize { get; set; } = string.Empty;

        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Upload date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last accessed date
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// Download URL
        /// </summary>
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Preview URL (for supported file types)
        /// </summary>
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// Whether current user can delete this document
        /// </summary>
        public bool CanDelete { get; set; } = true;

        /// <summary>
        /// Whether current user can download this document
        /// </summary>
        public bool CanDownload { get; set; } = true;
    }
}
