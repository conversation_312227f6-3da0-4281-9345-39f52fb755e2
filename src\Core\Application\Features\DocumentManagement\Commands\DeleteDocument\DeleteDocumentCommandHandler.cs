using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Domain.Entities.DocumentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.Notifications;
using Domain.Entities.Shared;
using Domain.Entities.FundManagement;

namespace Application.Features.DocumentManagement.Commands.DeleteDocument
{
    /// <summary>
    /// Handler for deleting documents
    /// </summary>
    public class DeleteDocumentCommandHandler : BaseResponseHandler, ICommandHandler<DeleteDocumentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public DeleteDocumentCommandHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<string>> Handle(DeleteDocumentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document deletion for document ID: {request.DocumentId} by user {_currentUserService.UserId}");

                // 1. Validate current user
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Check user permissions for document deletion
                if (!HasDocumentDeletePermission())
                {
                    _logger.LogWarn($"User {currentUserId.Value} does not have permission to delete documents");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Get document details before deletion for notification
                var document = await _repositoryManager.DocumentRepository.GetByIdAsync<Document>(request.DocumentId, false);
                if (document == null)
                {
                    _logger.LogWarn($"Document with ID {request.DocumentId} not found for deletion.");
                    return NotFound<string>(_localizer["DocumentNotFound"]);
                }

                // 4. Get attachment details for notification
                Attachment attachment = await _repositoryManager.Attachments.GetByIdAsync<Attachment>(document.AttachmentId, false);

                // 5. Get fund details for notification
                Fund fund =  await _repositoryManager.Funds.GetByIdAsync<Fund>(document.FundId, false);

                // 6. Remove document from database
                var result = await _repositoryManager.DocumentRepository.DeleteDocumentAsync(request.DocumentId);
                if (!result)
                {
                    _logger.LogWarn($"Failed to delete document with ID {request.DocumentId}.");
                    return ServerError<string>(_localizer["DocumentDeletionFailed"]);
                }

                _logger.LogInfo($"Document deleted successfully. ID: {request.DocumentId}");

                // 7. Send notifications after successful document deletion
                await SendDocumentDeletedNotifications(document, attachment, fund);

                return Success<string>(_localizer["DocumentDeletedSuccessfully"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting document ID {request.DocumentId}: {ex.Message}");
                return ServerError<string>(_localizer["DocumentDeletionFailed"]);
            }
        }
        #endregion

        #region Helper Methods

        /// <summary>
        /// Checks if the current user has permission to delete documents
        /// Based on document permissions defined in DocumentPermission constants
        /// </summary>
        private bool HasDocumentDeletePermission()
        {
            var userRoles = _currentUserService.Roles;

            // Check if user has Document.Delete permission through their roles
            // This follows the same pattern as other permission checks in the codebase
            return userRoles.Any(role =>
                role.Equals("legalcouncil", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Sends document deleted notifications to relevant stakeholders
        /// Follows the established notification pattern from other command handlers
        /// </summary>
        private async Task SendDocumentDeletedNotifications(Document document, Attachment attachment, Fund fund)
        {
            try
            {
                var notifications = new List<Domain.Entities.Notifications.Notification>();
                var currentUserId = _currentUserService.UserId.Value;
                var currentUserName = _currentUserService.UserName ?? "Unknown User";

                // Get stakeholders to notify based on fund association
                var stakeholderUserIds = new List<int>();

                if (fund != null)
                {
                    // Add Fund Managers
                    if (fund.FundManagers != null)
                    {
                        stakeholderUserIds.AddRange(fund.FundManagers.Where(fm => fm.IsDeleted != true).Select(fm => fm.UserId));
                    }

                    // Add Board Secretaries
                    if (fund.FundBoardSecretaries != null)
                    {
                        stakeholderUserIds.AddRange(fund.FundBoardSecretaries.Where(bs => bs.IsDeleted != true).Select(bs => bs.UserId));
                    }

                    // Add Legal Council
                    stakeholderUserIds.Add(fund.LegalCouncilId);

                    // Remove duplicates and exclude the current user (who deleted the document)
                    stakeholderUserIds = stakeholderUserIds.Distinct().Where(id => id != currentUserId).ToList();

                    // Create notifications for all stakeholders
                    foreach (var userId in stakeholderUserIds)
                    {
                        notifications.Add(new Domain.Entities.Notifications.Notification
                        {
                            Title = string.Empty, // Will be localized at send time
                            Body = $"{currentUserName}|{attachment?.FileName ?? "Unknown Document"}|{fund.Name}", // Store parameters for localization
                            FundId = fund.Id,
                            UserId = userId,
                            NotificationType = (int)NotificationType.DocumentDeleted, // MSG-N-03
                            NotificationModule = (int)NotificationModule.Documents,
                        });
                    }
                }
                else
                {
                    // For documents not associated with a fund, notify system administrators
                    // This follows the pattern from other handlers for system-wide notifications
                    _logger.LogInfo($"Document {document.Id} deleted without fund association - no notifications sent");
                }

                // Save notifications if any were created
                if (notifications.Any())
                {
                    await _repositoryManager.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Document deleted notifications sent for Document ID: {document.Id}, Count: {notifications.Count}");
                }
                else
                {
                    _logger.LogInfo($"No notifications to send for Document ID: {document.Id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending document deleted notifications for Document ID: {document.Id}");
                // Don't throw - notification failure shouldn't fail the document deletion
            }
        }

        #endregion
    }
}
