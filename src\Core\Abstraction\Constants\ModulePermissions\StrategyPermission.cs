﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class StrategyPermission
    {
        public const string View = "Strategy.View";
        public const string List = "Strategy.List";
        public const string Create = "Strategy.Create";
        public const string Edit = "Strategy.Edit";
        public const string Delete = "Strategy.Delete";
    }
}
