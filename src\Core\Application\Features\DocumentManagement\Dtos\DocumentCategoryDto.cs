using Abstraction.Base.Dto;

namespace Application.Features.DocumentManagement.Dtos
{
    /// <summary>
    /// Document category data transfer object
    /// </summary>
    public record DocumentCategoryDto : BaseDto
    {
        /// <summary>
        /// Category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether current user can upload to this category
        /// </summary>
        public bool CanUpload { get; set; } = true;
    }
}
