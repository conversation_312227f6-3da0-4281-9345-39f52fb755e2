﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class LegalCouncilPermission
    {
        public const string View = "Fund.View";
        public const string List = "Fund.List";
        public const string Create = "Fund.Create";
        public const string Complete = "Fund.Complete";
        public const string Edit = "Fund.Edit";
        public const string EditFundExitdate = "Fund.EditFundExitdate";
    }
}
