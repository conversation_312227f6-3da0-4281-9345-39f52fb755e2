using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.Notifications;
using Domain.Entities.Shared;
using Domain.Entities.FundManagement;

namespace Application.Features.DocumentManagement.Commands.Add
{
    /// <summary>
    /// Handler for adding documents
    /// </summary>
    public class AddDocumentCommandHandler : BaseResponseHandler, ICommandHandler<AddDocumentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IMapper _mapper;

        #endregion

        #region Constructor
        public AddDocumentCommandHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            IMapper mapper)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
            _mapper = mapper;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<string>> Handle(AddDocumentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document add for user {_currentUserService.UserId}");

                // 1. Validate current user
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Check user permissions for document creation
                if (!HasDocumentCreatePermission())
                {
                    _logger.LogWarn($"User {currentUserId.Value} does not have permission to create documents");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Validate category exists
                var category = await _repositoryManager.DocumentCategoryRepository.GetByIdAsync<DocumentCategory>(request.DocumentCategoryId, false);
                if (category == null)
                {
                    return BadRequest<string>(_localizer["DocumentCategoryNotFound"]);
                }

                // 4. Validate file
                if (request.AttachmentId == 0)
                {
                    return BadRequest<string>(_localizer["FileIsRequired"]);
                }

                // 5. Get attachment details for notification
                var attachment = await _repositoryManager.Attachments.GetByIdAsync<Attachment>(request.AttachmentId, false);
                if (attachment == null)
                {
                    return BadRequest<string>(_localizer["AttachmentNotFound"]);
                }

                // 6. Get fund details for notification
                Fund fund = await _repositoryManager.Funds.GetByIdAsync<Fund>(request.FundId, false); ;
                if (fund == null)
                {
                   return BadRequest<string>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 7. Create document entity with reference to attachment
                var document = _mapper.Map<Document>(request);

                await _repositoryManager.DocumentRepository.AddAsync(document);
                _logger.LogInfo($"Document added successfully. ID: {document.Id}");

                // 8. Send notifications after successful document addition
                await SendDocumentAddedNotifications(document, attachment, fund);

                return Success<string>(_localizer["DocumentAddedSuccessfully"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error adding document: {ex.Message}");
                return ServerError<string>(_localizer["DocumentAddFailed"]);
            }
        }
        #endregion

        #region Helper Methods

        /// <summary>
        /// Checks if the current user has permission to create documents
        /// Based on document permissions defined in DocumentPermission constants
        /// </summary>
        private bool HasDocumentCreatePermission()
        {
            var userRoles = _currentUserService.Roles;

            // Check if user has Document.Create permission through their roles
            // This follows the same pattern as other permission checks in the codebase
            return userRoles.Any(role =>
                role.Equals("fundmanager", StringComparison.OrdinalIgnoreCase) ||
                role.Equals("boardsecretary", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Sends document added notifications to relevant stakeholders
        /// Follows the established notification pattern from other command handlers
        /// </summary>
        private async Task SendDocumentAddedNotifications(Document document, Attachment attachment, Fund fund)
        {
            try
            {
                var notifications = new List<Domain.Entities.Notifications.Notification>();
                var currentUserId = _currentUserService.UserId.Value;
                var currentUserName = _currentUserService.UserName ?? "Unknown User";

                // Get stakeholders to notify based on fund association
                var stakeholderUserIds = new List<int>();

                if (fund != null)
                {
                    // Add Fund Managers
                    if (fund.FundManagers != null)
                    {
                        stakeholderUserIds.AddRange(fund.FundManagers.Where(fm => fm.IsDeleted != true).Select(fm => fm.UserId));
                    }

                    // Add Board Secretaries
                    if (fund.FundBoardSecretaries != null)
                    {
                        stakeholderUserIds.AddRange(fund.FundBoardSecretaries.Where(bs => bs.IsDeleted != true).Select(bs => bs.UserId));
                    }

                    // Add Legal Council
                    stakeholderUserIds.Add(fund.LegalCouncilId);

                    // Remove duplicates and exclude the current user (who added the document)
                    stakeholderUserIds = stakeholderUserIds.Distinct().Where(id => id != currentUserId).ToList();

                    // Create notifications for all stakeholders
                    foreach (var userId in stakeholderUserIds)
                    {
                        notifications.Add(new Domain.Entities.Notifications.Notification
                        {
                            Title = string.Empty, // Will be localized at send time
                            Body = $"{currentUserName}|{attachment.FileName}|{fund.Name}", // Store parameters for localization
                            FundId = fund.Id,
                            UserId = userId,
                            NotificationType = (int)NotificationType.DocumentAdded, // MSG-N-01
                            NotificationModule = (int)NotificationModule.Documents,
                        });
                    }
                }
                else
                {
                    // For documents not associated with a fund, notify system administrators
                    // This follows the pattern from other handlers for system-wide notifications
                    _logger.LogInfo($"Document {document.Id} added without fund association - no notifications sent");
                }

                // Save notifications if any were created
                if (notifications.Any())
                {
                    await _repositoryManager.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Document added notifications sent for Document ID: {document.Id}, Count: {notifications.Count}");
                }
                else
                {
                    _logger.LogInfo($"No notifications to send for Document ID: {document.Id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending document added notifications for Document ID: {document.Id}");
                // Don't throw - notification failure shouldn't fail the document creation
            }
        }

        #endregion
    }
}
