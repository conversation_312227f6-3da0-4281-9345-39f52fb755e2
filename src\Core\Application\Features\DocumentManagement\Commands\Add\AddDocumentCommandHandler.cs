using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Enums;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Application.Features.Shared.FileManagment.Commands.Add;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using MediatR;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.DocumentManagement.Commands.Add
{
    /// <summary>
    /// Handler for adding documents
    /// </summary>
    public class AddDocumentCommandHandler : BaseResponseHandler, ICommandHandler<AddDocumentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IMapper _mapper;

        #endregion

        #region Constructor
        public AddDocumentCommandHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            IMapper mapper)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
            _mapper = mapper;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<string>> Handle(AddDocumentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document add for user {_currentUserService.UserId}");

                // Validate category exists
                var category = await _repositoryManager.DocumentCategoryRepository.GetByIdAsync<DocumentCategory>(request.DocumentCategoryId, false);
                if (category == null)
                {
                    return BadRequest<string>(_localizer["DocumentCategoryNotFound"]);
                }

                // Validate file
                if (request.AttachmentId == 0)
                {
                    return BadRequest<string>(_localizer["FileIsRequired"]);
                }

                // Create document entity with reference to attachment
                var document = _mapper.Map<Document>(request);

                await _repositoryManager.DocumentRepository.AddAsync(document);
                _logger.LogInfo($"Document added successfully. ID: {document.Id}");


                return Success<string>(_localizer["DocumentAddedSuccessfully"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error adding document: {ex.Message}");
                return ServerError<string>(_localizer["DocumentAddFailed"]);
            }
        }
        #endregion

        #region Helper Methods
        #endregion
    }
}
