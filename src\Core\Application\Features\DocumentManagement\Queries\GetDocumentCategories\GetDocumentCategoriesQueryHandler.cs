using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Resources;
using System.Globalization;

namespace Application.Features.DocumentManagement.Queries.GetDocumentCategories
{
    /// <summary>
    /// Handler for getting document categories
    /// </summary>
    public class GetDocumentCategoriesQueryHandler : BaseResponseHandler, IQueryHandler<GetDocumentCategoriesQuery, BaseResponse<List<DocumentCategoryDto>>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public GetDocumentCategoriesQueryHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<List<DocumentCategoryDto>>> Handle(GetDocumentCategoriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting document categories for user {_currentUserService.UserId}");

                // Get categories
                var categories = await _repositoryManager.DocumentCategoryRepository.GetCategoriesAsync();

                // Map to DTOs
                var categoriesDtos = _mapper.Map<List<DocumentCategoryDto>>(categories);

                return Success(categoriesDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting document categories: {ex.Message}");
                return ServerError<List<DocumentCategoryDto>>(_localizer["ErrorGettingDocumentCategories"]);
            }
        }
        #endregion

        #region Helper Methods
       
        #endregion
    }
}
