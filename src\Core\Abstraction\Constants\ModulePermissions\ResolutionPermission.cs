﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class ResolutionPermission
    {
        public const string View = "Resolution.View";
        public const string List = "Resolution.List";
        public const string Create = "Resolution.Create";
        public const string Edit = "Resolution.Edit";
        public const string Delete = "Resolution.Delete";
        public const string Cancel = "Resolution.Cancel";
    }
}
