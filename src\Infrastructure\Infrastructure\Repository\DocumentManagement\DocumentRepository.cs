using Abstraction.Contract.Repository.DocumentManagement;
using Abstraction.Contract.Service;
using Domain.Entities.DocumentManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.DocumentManagement
{
    /// <summary>
    /// Repository implementation for Document entity operations
    /// </summary>
    public class DocumentRepository : GenericRepository, IDocumentRepository
    {
        #region Constructor
        public DocumentRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get documents with filters and includes
        /// </summary>
        public IQueryable<Document> GetDocumentsWithFiltersAsync(
            int categoryId,
            int fundId,
            string? searchTerm = null)
        {
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.Attachment)
                .Where(d => d.DocumentCategoryId == categoryId && d.FundId == fundId).AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(d =>
                    d.Attachment.FileName.ToLower().Contains(lowerSearchTerm));
            }
            return query.OrderByDescending(d => d.UpdatedAt ?? d.CreatedAt);
        }
        public async Task<bool> DeleteDocumentAsync(int documentId)
        {
            try
            {
                var document = await GetByIdAsync<Document>(documentId, trackChanges: false);
                if (document == null)
                {
                    return false; // Document not found
                }
                return await DeleteAsync(document);
            }
            catch (Exception ex)
            {
                // Log exception or handle it as needed
                throw new Exception("Error deleting document", ex);
            }
            #endregion
        }
    }
}
