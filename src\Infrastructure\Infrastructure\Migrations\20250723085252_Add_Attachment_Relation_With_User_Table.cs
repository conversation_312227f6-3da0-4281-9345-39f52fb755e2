﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Add_Attachment_Relation_With_User_Table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_CreatedBy",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_DeletedBy",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_UpdatedBy",
                table: "Attachments");

            migrationBuilder.AddColumn<int>(
                name: "CvFileId",
                table: "AspNetUsers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UserAuditHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    ActionDetails = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NewStatus = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PreviousStatus = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserRole = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Action = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AdditionalData = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserAuditHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserAuditHistories_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UserAuditHistories_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_CvFileId",
                table: "AspNetUsers",
                column: "CvFileId");

            migrationBuilder.CreateIndex(
                name: "IX_UserAuditHistories_CreatedBy",
                table: "UserAuditHistories",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_UserAuditHistories_UserId",
                table: "UserAuditHistories",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUsers_Attachments_CvFileId",
                table: "AspNetUsers",
                column: "CvFileId",
                principalTable: "Attachments",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_CreatedBy",
                table: "Attachments",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_DeletedBy",
                table: "Attachments",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_UpdatedBy",
                table: "Attachments",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUsers_Attachments_CvFileId",
                table: "AspNetUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_CreatedBy",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_DeletedBy",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_AspNetUsers_UpdatedBy",
                table: "Attachments");

            migrationBuilder.DropTable(
                name: "UserAuditHistories");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_CvFileId",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CvFileId",
                table: "AspNetUsers");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_CreatedBy",
                table: "Attachments",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_DeletedBy",
                table: "Attachments",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_AspNetUsers_UpdatedBy",
                table: "Attachments",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
