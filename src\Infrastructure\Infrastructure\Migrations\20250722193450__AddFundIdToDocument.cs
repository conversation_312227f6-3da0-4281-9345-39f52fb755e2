﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class _AddFundIdToDocument : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FundId",
                table: "Documents",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Documents_FundId",
                table: "Documents",
                column: "FundId");

            migrationBuilder.AddForeignKey(
                name: "FK_Documents_Funds_FundId",
                table: "Documents",
                column: "FundId",
                principalTable: "Funds",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Documents_Funds_FundId",
                table: "Documents");

            migrationBuilder.DropIndex(
                name: "IX_Documents_FundId",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "FundId",
                table: "Documents");
        }
    }
}
