﻿// <auto-generated />
using System;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Infrastructure.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250630205722_Fix_Cascading_ReolutionAttachment_table")]
    partial class Fix_Cascading_ReolutionAttachment_table
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Domain.Entities.FundManagement.BoardMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to Fund entity");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasComment("Status of the board member (Active/Inactive)");

                    b.Property<bool>("IsChairman")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasComment("Indicates if this board member is the chairman");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("MemberType")
                        .HasColumnType("int")
                        .HasComment("Type of board member (Independent=1, NotIndependent=2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to User entity");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("FundId")
                        .HasDatabaseName("IX_BoardMembers_FundId");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_BoardMembers_UserId");

                    b.HasIndex("FundId", "IsChairman")
                        .HasDatabaseName("IX_BoardMembers_Fund_Chairman")
                        .HasFilter("[IsChairman] = 1 AND [IsDeleted] = 0");

                    b.HasIndex("FundId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_BoardMembers_Fund_User_Unique")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("FundId", "MemberType", "IsActive")
                        .HasDatabaseName("IX_BoardMembers_Fund_Type_Active")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("BoardMembers", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.Fund", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExitDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("InitiationDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("LegalCouncilId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("OldCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PropertiesNumber")
                        .HasColumnType("int");

                    b.Property<int>("StrategyId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("VotingTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Fund_CreatedAt");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_Fund_CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ExitDate")
                        .HasDatabaseName("IX_Funds_ExitDate")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("InitiationDate")
                        .HasDatabaseName("IX_Funds_InitiationDate")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_Fund_IsDeleted");

                    b.HasIndex("LegalCouncilId")
                        .HasDatabaseName("IX_Funds_LegalCouncilId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Funds_Name")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("StrategyId")
                        .HasDatabaseName("IX_Funds_StrategyId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_Fund_UpdatedAt");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("CreatedAt", "CreatedBy")
                        .HasDatabaseName("IX_Funds_Audit_Created")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("StrategyId", "InitiationDate")
                        .HasDatabaseName("IX_Funds_Strategy_InitiationDate")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("Name", "InitiationDate", "StrategyId")
                        .HasDatabaseName("IX_Funds_Search_Composite")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("Funds");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundBoardSecretary", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FundBoardSecretary_CreatedAt");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_FundBoardSecretary_CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("FundId")
                        .HasDatabaseName("IX_FundBoardSecretaries_FundId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_FundBoardSecretary_IsDeleted");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_FundBoardSecretary_UpdatedAt");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_FundBoardSecretaries_UserId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("FundId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_FundBoardSecretaries_Fund_User_Unique")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("FundBoardSecretaries");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundManager", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FundManager_CreatedAt");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_FundManager_CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("FundId")
                        .HasDatabaseName("IX_FundManagers_FundId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_FundManager_IsDeleted");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_FundManager_UpdatedAt");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_FundManagers_UserId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("FundId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_FundManagers_Fund_User_Unique")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("FundManagers");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("FundId");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("UserId");

                    b.ToTable("FundMembers");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundStatusHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("StatusHistoryId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FundStatusHistory_CreatedAt");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_FundStatusHistory_CreatedBy")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_FundStatusHistory_IsDeleted");

                    b.HasIndex("StatusHistoryId")
                        .HasDatabaseName("IX_FundStatusHistory_StatusId")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_FundStatusHistory_UpdatedAt");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("FundId", "CreatedAt")
                        .IsDescending(false, true)
                        .HasDatabaseName("IX_FundStatusHistory_Fund_CreatedAt_DESC")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("FundStatusHistories");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.StatusHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("StatusHistories");
                });

            modelBuilder.Entity("Domain.Entities.Notifications.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<int>("FundId")
                        .HasColumnType("int");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSent")
                        .HasColumnType("bit");

                    b.Property<int>("NotificationType")
                        .HasColumnType("int");

                    b.Property<DateTime>("SentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Notification_CreatedAt");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_Notification_CreatedBy");

                    b.HasIndex("FundId")
                        .HasDatabaseName("IX_Notifications_FundId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_Notifications_UserId");

                    b.HasIndex("IsSent", "CreatedAt")
                        .HasDatabaseName("IX_Notifications_IsSent_CreatedAt");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.Resolution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to main Attachment entity");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Auto-generated resolution code (fund code/resolution year/resolution no.)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("Description of the resolution (optional, max 500 characters)");

                    b.Property<int>("FundId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to Fund entity");

                    b.Property<int?>("FundId1")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("MemberVotingResult")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(2)
                        .HasComment("How member voting results are calculated");

                    b.Property<string>("NewType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("Custom resolution type name when 'Other' is selected");

                    b.Property<string>("OldResolutionCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Old resolution code when this is an update to existing resolution");

                    b.Property<int?>("ParentResolutionId")
                        .HasColumnType("int")
                        .HasComment("Parent resolution identifier for tracking relationships");

                    b.Property<DateTime>("ResolutionDate")
                        .HasColumnType("datetime2")
                        .HasComment("Date of the resolution");

                    b.Property<int>("ResolutionTypeId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to ResolutionType entity");

                    b.Property<int?>("ResolutionTypeId1")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("Current status of the resolution");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("VotingType")
                        .HasColumnType("int")
                        .HasComment("Voting methodology (AllMembers=1, Majority=2)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Resolutions_Code_Unique");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("FundId")
                        .HasDatabaseName("IX_Resolutions_FundId");

                    b.HasIndex("FundId1");

                    b.HasIndex("ParentResolutionId")
                        .HasDatabaseName("IX_Resolutions_ParentId");

                    b.HasIndex("ResolutionDate")
                        .HasDatabaseName("IX_Resolutions_Date");

                    b.HasIndex("ResolutionTypeId");

                    b.HasIndex("ResolutionTypeId1");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Resolutions_Status");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("FundId", "Status")
                        .HasDatabaseName("IX_Resolutions_Fund_Status");

                    b.ToTable("Resolutions", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to Attachment entity");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("ResolutionId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to Resolution entity");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId")
                        .HasDatabaseName("IX_ResolutionAttachments_AttachmentId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ResolutionId")
                        .HasDatabaseName("IX_ResolutionAttachments_ResolutionId");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("ResolutionId", "AttachmentId")
                        .IsUnique()
                        .HasDatabaseName("IX_ResolutionAttachments_Resolution_Attachment_Unique")
                        .HasFilter("([IsDeleted] = 0 OR [IsDeleted] IS NULL)");

                    b.ToTable("ResolutionAttachments", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("Description of the resolution item (max 500 characters)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int")
                        .HasComment("Display order for sorting resolution items");

                    b.Property<bool>("HasConflict")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasComment("Indicates if there is a conflict of interest with board members");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("ResolutionId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to Resolution entity");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("Auto-generated title (Item1, Item2, etc.)");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ResolutionId")
                        .HasDatabaseName("IX_ResolutionItems_ResolutionId");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("ResolutionId", "DisplayOrder")
                        .HasDatabaseName("IX_ResolutionItems_Resolution_Order");

                    b.HasIndex("ResolutionId", "HasConflict")
                        .HasDatabaseName("IX_ResolutionItems_Resolution_Conflict");

                    b.ToTable("ResolutionItems", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionItemConflict", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BoardMemberId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to BoardMember entity");

                    b.Property<string>("ConflictNotes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("Optional notes about the nature of the conflict");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("ResolutionItemId")
                        .HasColumnType("int")
                        .HasComment("Foreign key reference to ResolutionItem entity");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BoardMemberId")
                        .HasDatabaseName("IX_ResolutionItemConflicts_BoardMemberId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ResolutionItemId")
                        .HasDatabaseName("IX_ResolutionItemConflicts_ResolutionItemId");

                    b.HasIndex("UpdatedBy");

                    b.HasIndex("ResolutionItemId", "BoardMemberId")
                        .IsUnique()
                        .HasDatabaseName("IX_ResolutionItemConflicts_Item_Member_Unique")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("ResolutionItemConflicts", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ResolutionStatus");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionStatusHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<string>("ActionDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int?>("NewStatus")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PreviousStatus")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RejectionReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ResolutionId")
                        .HasColumnType("int");

                    b.Property<int>("ResolutionStatusId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<string>("UserRole")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ResolutionId");

                    b.HasIndex("ResolutionStatusId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("ResolutionStatusHistories");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOther")
                        .HasColumnType("bit");

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("ResolutionTypes");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionVote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BoardMemberId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("ResolutionId")
                        .HasColumnType("int");

                    b.Property<int?>("ResolutionItemId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.Property<int>("VoteValue")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BoardMemberId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("ResolutionId");

                    b.HasIndex("ResolutionItemId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("ResolutionVotes");
                });

            modelBuilder.Entity("Domain.Entities.Shared.Attachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("ModuleId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("Attachments");
                });

            modelBuilder.Entity("Domain.Entities.Startegies.Strategy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeletedBy")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Strategy_CreatedAt")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_Strategy_CreatedBy");

                    b.HasIndex("DeletedBy");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_Strategy_IsDeleted");

                    b.HasIndex("NameAr")
                        .HasDatabaseName("IX_Strategies_NameAr")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("NameEn")
                        .HasDatabaseName("IX_Strategies_NameEn")
                        .HasFilter("[IsDeleted] = 0");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_Strategy_UpdatedAt");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("Strategies");
                });

            modelBuilder.Entity("Domain.Entities.Users.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Users.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("PreferredLanguage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Email")
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("FullName")
                        .HasDatabaseName("IX_Users_FullName");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex("PhoneNumber")
                        .HasDatabaseName("IX_Users_PhoneNumber");

                    b.HasIndex("UserName")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_UserName")
                        .HasFilter("[UserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Users.UserRefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("AddedTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("JwtId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RefreshToken")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserRefreshTokens", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.BoardMember", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany("BoardMembers")
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_BoardMembers_Funds");

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_BoardMembers_Users");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("UpdatedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.Fund", b =>
                {
                    b.HasOne("Domain.Entities.Shared.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "LegalCouncil")
                        .WithMany()
                        .HasForeignKey("LegalCouncilId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Startegies.Strategy", "Strategy")
                        .WithMany()
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Attachment");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("LegalCouncil");

                    b.Navigation("Strategy");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundBoardSecretary", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany("FundBoardSecretaries")
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("UpdatedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundManager", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany("FundManagers")
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("UpdatedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundMember", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany()
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("UpdatedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.FundStatusHistory", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany("FundStatusHistories")
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.FundManagement.StatusHistory", "StatusHistory")
                        .WithMany()
                        .HasForeignKey("StatusHistoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("StatusHistory");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.Notifications.Notification", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.FundManagement.Fund", null)
                        .WithMany("Notifications")
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.Resolution", b =>
                {
                    b.HasOne("Domain.Entities.Shared.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Resolutions_Attachments");

                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.FundManagement.Fund", "Fund")
                        .WithMany()
                        .HasForeignKey("FundId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Resolutions_Funds");

                    b.HasOne("Domain.Entities.FundManagement.Fund", null)
                        .WithMany("Resolutions")
                        .HasForeignKey("FundId1");

                    b.HasOne("Domain.Entities.ResolutionManagement.Resolution", "ParentResolution")
                        .WithMany("ChildResolutions")
                        .HasForeignKey("ParentResolutionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("FK_Resolutions_ParentResolution");

                    b.HasOne("Domain.Entities.ResolutionManagement.ResolutionType", "ResolutionType")
                        .WithMany()
                        .HasForeignKey("ResolutionTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Resolutions_ResolutionTypes");

                    b.HasOne("Domain.Entities.ResolutionManagement.ResolutionType", null)
                        .WithMany("Resolutions")
                        .HasForeignKey("ResolutionTypeId1");

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Attachment");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Fund");

                    b.Navigation("ParentResolution");

                    b.Navigation("ResolutionType");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionAttachment", b =>
                {
                    b.HasOne("Domain.Entities.Shared.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_ResolutionAttachments_Attachments");

                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.ResolutionManagement.Resolution", "Resolution")
                        .WithMany("OtherAttachments")
                        .HasForeignKey("ResolutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ResolutionAttachments_Resolutions");

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Attachment");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Resolution");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionItem", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.ResolutionManagement.Resolution", "Resolution")
                        .WithMany("ResolutionItems")
                        .HasForeignKey("ResolutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ResolutionItems_Resolutions");

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Resolution");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionItemConflict", b =>
                {
                    b.HasOne("Domain.Entities.FundManagement.BoardMember", "BoardMember")
                        .WithMany("ConflictItems")
                        .HasForeignKey("BoardMemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_ResolutionItemConflicts_BoardMembers");

                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.ResolutionManagement.ResolutionItem", "ResolutionItem")
                        .WithMany("ConflictMembers")
                        .HasForeignKey("ResolutionItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ResolutionItemConflicts_ResolutionItems");

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("BoardMember");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("ResolutionItem");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionStatusHistory", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.ResolutionManagement.Resolution", "Resolution")
                        .WithMany("ResolutionStatusHistories")
                        .HasForeignKey("ResolutionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.ResolutionManagement.ResolutionStatus", "ResolutionStatus")
                        .WithMany()
                        .HasForeignKey("ResolutionStatusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Resolution");

                    b.Navigation("ResolutionStatus");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionType", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionVote", b =>
                {
                    b.HasOne("Domain.Entities.FundManagement.BoardMember", "BoardMember")
                        .WithMany("Votes")
                        .HasForeignKey("BoardMemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.ResolutionManagement.Resolution", "Resolution")
                        .WithMany("Votes")
                        .HasForeignKey("ResolutionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.ResolutionManagement.ResolutionItem", "ResolutionItem")
                        .WithMany("Votes")
                        .HasForeignKey("ResolutionItemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("BoardMember");

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("Resolution");

                    b.Navigation("ResolutionItem");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.Shared.Attachment", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.Startegies.Strategy", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", "DeletedByUser")
                        .WithMany()
                        .HasForeignKey("DeletedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("DeletedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Domain.Entities.Users.UserRefreshToken", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Domain.Entities.Users.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Domain.Entities.Users.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("Domain.Entities.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.BoardMember", b =>
                {
                    b.Navigation("ConflictItems");

                    b.Navigation("Votes");
                });

            modelBuilder.Entity("Domain.Entities.FundManagement.Fund", b =>
                {
                    b.Navigation("BoardMembers");

                    b.Navigation("FundBoardSecretaries");

                    b.Navigation("FundManagers");

                    b.Navigation("FundStatusHistories");

                    b.Navigation("Notifications");

                    b.Navigation("Resolutions");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.Resolution", b =>
                {
                    b.Navigation("ChildResolutions");

                    b.Navigation("OtherAttachments");

                    b.Navigation("ResolutionItems");

                    b.Navigation("ResolutionStatusHistories");

                    b.Navigation("Votes");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionItem", b =>
                {
                    b.Navigation("ConflictMembers");

                    b.Navigation("Votes");
                });

            modelBuilder.Entity("Domain.Entities.ResolutionManagement.ResolutionType", b =>
                {
                    b.Navigation("Resolutions");
                });
#pragma warning restore 612, 618
        }
    }
}
